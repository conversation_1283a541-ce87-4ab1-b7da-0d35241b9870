'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { HeaderNavigation } from '@/modules/home/<USER>/new-home/HeaderNavigation'
import { clearAuthTokens, setToken } from '@/services/auth'
import { useEffect, useState } from 'react'

export default function TestHeaderPage() {
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  useEffect(() => {
    // Check if user is authenticated
    const token = localStorage.getItem('token')
    setIsAuthenticated(!!token)
  }, [])

  const handleLogin = () => {
    // Simulate login by setting a fake token
    setToken('fake-token-for-testing')
    setIsAuthenticated(true)
  }

  const handleLogout = () => {
    // Clear tokens
    clearAuthTokens()
    localStorage.removeItem('token')
    setIsAuthenticated(false)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Navigation */}
      <HeaderNavigation />

      {/* Test Content */}
      <div className="pt-20 pb-8">
        <div className="container mx-auto px-4">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle>Test Header Navigation</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <span className="font-medium">
                  Authentication Status:
                  <span className={`ml-2 ${isAuthenticated ? 'text-green-600' : 'text-red-600'}`}>
                    {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
                  </span>
                </span>

                <div className="flex gap-2">
                  {isAuthenticated
                    ? (
                        <Button onClick={handleLogout} variant="outline" size="sm">
                          Logout (Test)
                        </Button>
                      )
                    : (
                        <Button onClick={handleLogin} variant="outline" size="sm">
                          Login (Test)
                        </Button>
                      )}
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="font-semibold">Test Instructions:</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Click "Login (Test)" to simulate authentication</li>
                  <li>• Click "Logout (Test)" to clear authentication</li>
                  <li>• Observe how the header navigation changes</li>
                  <li>• Test responsive behavior by resizing the window</li>
                  <li>• Check that all navigation links work correctly</li>
                </ul>
              </div>

              <div className="space-y-2">
                <h3 className="font-semibold">Expected Behavior:</h3>
                <div className="text-sm text-gray-600 space-y-1">
                  <div><strong>When Not Authenticated:</strong></div>
                  <ul className="ml-4 space-y-1">
                    <li>• Shows "Đăng nhập" button</li>
                    <li>• Shows "Đăng ký" button</li>
                    <li>• Shows "Bắt đầu" CTA button</li>
                  </ul>

                  <div className="mt-2"><strong>When Authenticated:</strong></div>
                  <ul className="ml-4 space-y-1">
                    <li>• Shows "Dashboard" button</li>
                    <li>• Shows "Quản lý" button</li>
                    <li>• Hides guest navigation</li>
                  </ul>

                  <div className="mt-2"><strong>Framer Motion Animations:</strong></div>
                  <ul className="ml-4 space-y-1">
                    <li>• Header slides down from top (y: -100 → 0)</li>
                    <li>• Logo and navigation items stagger in</li>
                    <li>• Smooth easeOut transitions</li>
                    <li>• No GSAP dependency needed</li>
                  </ul>
                </div>
              </div>

              <div className="p-4 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Note:</strong>
                  {' '}
                  This is a test page. The authentication simulation only affects this page.
                  Real authentication will be handled by the actual auth system.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
