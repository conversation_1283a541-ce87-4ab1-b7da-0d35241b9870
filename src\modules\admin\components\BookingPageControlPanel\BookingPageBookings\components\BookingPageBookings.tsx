'use client'

import type { AdminBookingItem } from '@/modules/admin/apis/booking-page.api'
import type { BookingFilters, BookingPageBookingsProps } from '../types'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Calendar, Download, Filter, Plus, RefreshCw, Search } from 'lucide-react'
import React, { useMemo, useState } from 'react'
import { useBookingPageBookings } from '../hooks'
import {
  applyAllFilters,
  formatBookingSlots,
  formatDisplayDate,
  formatDisplayDateTime,
  getPaymentMethodDisplayText,
  getStatusBadgeVariant,
  getStatusDisplayText,
} from '../utils'

/**
 * Bookings tab for the booking page control panel
 * Shows bookings for the page and allows filtering and management
 */
const BookingPageBookings: React.FC<BookingPageBookingsProps> = ({ bookingPageId }) => {
  const [bookingTab, setBookingTab] = useState<BookingFilters['bookingTab']>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<BookingFilters['statusFilter']>('all')
  const [dateFilter, setDateFilter] = useState<BookingFilters['dateFilter']>('all')

  // Use the custom hook to fetch bookings
  const {
    bookings,
    isLoading,
    error,
    total,
    refetch,
  } = useBookingPageBookings({
    bookingPageId,
    status: statusFilter === 'all' ? undefined : statusFilter,
  })

  // Create filters object
  const filters: BookingFilters = {
    bookingTab,
    searchQuery,
    statusFilter,
    dateFilter,
  }

  // Filter bookings based on all criteria
  const filteredBookings = useMemo(() => {
    return bookings.filter((booking: AdminBookingItem) => applyAllFilters(booking, filters))
  }, [bookings, filters])

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">Quản lý đặt lịch</h2>
        <div className="flex gap-2">
          <Button
            variant="outline"
            className="flex items-center gap-1"
            onClick={refetch}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Làm mới
          </Button>
          <Button variant="outline" className="flex items-center gap-1">
            <Download className="h-4 w-4" />
            Xuất Excel
          </Button>
          <Button className="flex items-center gap-1">
            <Plus className="h-4 w-4" />
            Tạo đặt lịch
          </Button>
        </div>
      </div>

      <Tabs value={bookingTab} onValueChange={(value: string) => setBookingTab(value as BookingFilters['bookingTab'])}>
        <TabsList className="grid grid-cols-4 w-full md:w-[400px]">
          <TabsTrigger value="all">Tất cả</TabsTrigger>
          <TabsTrigger value="pending">Chờ xác nhận</TabsTrigger>
          <TabsTrigger value="confirmed">Đã xác nhận</TabsTrigger>
          <TabsTrigger value="cancelled">Đã hủy</TabsTrigger>
        </TabsList>
      </Tabs>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
            <div className="relative flex-1 w-full md:max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Tìm kiếm theo tên, email, số điện thoại..."
                className="pl-10"
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2 w-full md:w-auto">
              <Select value={statusFilter} onValueChange={value => setStatusFilter(value as BookingFilters['statusFilter'])}>
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder="Trạng thái" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả trạng thái</SelectItem>
                  <SelectItem value="pending">Chờ xác nhận</SelectItem>
                  <SelectItem value="confirmed">Đã xác nhận</SelectItem>
                  <SelectItem value="cancelled">Đã hủy</SelectItem>
                </SelectContent>
              </Select>
              <Select value={dateFilter} onValueChange={value => setDateFilter(value as BookingFilters['dateFilter'])}>
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder="Thời gian" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả thời gian</SelectItem>
                  <SelectItem value="today">Hôm nay</SelectItem>
                  <SelectItem value="tomorrow">Ngày mai</SelectItem>
                  <SelectItem value="thisWeek">Tuần này</SelectItem>
                  <SelectItem value="thisMonth">Tháng này</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="text-red-500 text-sm mb-4 p-3 bg-red-50 rounded-md">
              {error}
            </div>
          )}

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Khách hàng</TableHead>
                  <TableHead>Thời gian đặt</TableHead>
                  <TableHead>Trạng thái</TableHead>
                  <TableHead>Phương thức thanh toán</TableHead>
                  <TableHead className="text-right">Thao tác</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading
                  ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-6 text-gray-500">
                          Đang tải dữ liệu...
                        </TableCell>
                      </TableRow>
                    )
                  : filteredBookings.length === 0
                    ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-6 text-gray-500">
                            Không có đặt lịch nào phù hợp với bộ lọc
                          </TableCell>
                        </TableRow>
                      )
                    : (
                        filteredBookings.map((booking: AdminBookingItem) => {
                          const slotsData = formatBookingSlots(booking.bookingSlots)
                          return (
                            <TableRow key={booking._id}>
                              <TableCell>
                                <div>
                                  <div className="font-medium">{booking.customerName}</div>
                                  <div className="text-sm text-gray-500">{booking.customerPhone}</div>
                                  <div className="text-sm text-gray-500">{booking.customerEmail}</div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-start">
                                  <Calendar className="h-4 w-4 mr-2 text-gray-500 mt-1" />
                                  <div className="text-sm">
                                    <div className="font-medium mb-2">
                                      {formatDisplayDate(booking.bookingDate)}
                                    </div>
                                    <div className="space-y-2">
                                      {slotsData.slotsInfo !== 'Không có slot'
                                        ? (
                                            (slotsData.slotsInfo as Array<{ date: string, fieldTimes: string }>).map(slotInfo => (
                                              <div key={slotInfo.date} className="bg-gray-50 p-2 rounded">
                                                <div className="text-sm font-medium text-gray-700">{slotInfo.fieldTimes}</div>
                                              </div>
                                            ))
                                          )
                                        : (
                                            <div className="text-gray-500">Không có slot</div>
                                          )}
                                    </div>
                                    <div className="text-xs text-gray-400 mt-2 pt-2 border-t border-gray-100">
                                      Tạo:
                                      {' '}
                                      {formatDisplayDateTime(booking.createdAt)}
                                    </div>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant={getStatusBadgeVariant(booking.status)}>
                                  {getStatusDisplayText(booking.status)}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <div className="text-sm">
                                  {getPaymentMethodDisplayText(booking.paymentMethod)}
                                </div>
                              </TableCell>
                              <TableCell className="text-right">
                                <Button variant="ghost" size="sm">Chi tiết</Button>
                              </TableCell>
                            </TableRow>
                          )
                        })
                      )}
              </TableBody>
            </Table>
          </div>

          {total > 0 && (
            <div className="flex items-center justify-between mt-4 text-sm text-gray-500">
              <div>
                Hiển thị
                {' '}
                {filteredBookings.length}
                {' '}
                /
                {' '}
                {total}
                {' '}
                đặt lịch
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default BookingPageBookings
