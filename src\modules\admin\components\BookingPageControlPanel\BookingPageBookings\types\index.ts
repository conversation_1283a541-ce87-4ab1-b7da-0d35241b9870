export interface BookingPageBookingsProps {
  bookingPageId: string
}

export interface BookingSlotInfo {
  date: string
  fieldTimes: string
}

export interface FormattedBookingSlotsData {
  dates: string[]
  slotsInfo: BookingSlotInfo[] | string
}

export type BookingTabType = 'all' | 'pending' | 'confirmed' | 'cancelled'
export type DateFilterType = 'all' | 'today' | 'tomorrow' | 'thisWeek' | 'thisMonth'
export type StatusFilterType = 'all' | 'pending' | 'confirmed' | 'cancelled'

export interface BookingFilters {
  bookingTab: BookingTabType
  searchQuery: string
  statusFilter: StatusFilterType
  dateFilter: DateFilterType
}
