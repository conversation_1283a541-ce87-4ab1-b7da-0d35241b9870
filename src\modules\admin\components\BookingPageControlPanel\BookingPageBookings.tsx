'use client'

import type { AdminBookingItem } from '@/modules/admin/apis/booking-page.api'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useBookingPageBookings } from '@/modules/admin/hooks/useBookingPageBookings'
import { getFieldName } from '@/utils/field-booking'
import { format } from 'date-fns'
import { vi } from 'date-fns/locale'
import { Calendar, Download, Filter, Plus, RefreshCw, Search } from 'lucide-react'
import React, { useMemo, useState } from 'react'

interface BookingPageBookingsProps {
  bookingPageId: string
}

/**
 * Bookings tab for the booking page control panel
 * Shows bookings for the page and allows filtering and management
 */
const BookingPageBookings: React.FC<BookingPageBookingsProps> = ({ bookingPageId }) => {
  const [bookingTab, setBookingTab] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('all')

  // Use the custom hook to fetch bookings
  const {
    bookings,
    isLoading,
    error,
    total,
    refetch,
  } = useBookingPageBookings({
    bookingPageId,
    status: statusFilter === 'all' ? undefined : statusFilter,
  })

  // Helper function to format booking slots display
  const formatBookingSlots = (slots: AdminBookingItem['bookingSlots']) => {
    if (!slots || slots.length === 0) {
      return { dates: [], slotsInfo: 'Không có slot' }
    }

    // Group slots by date
    const slotsByDate = slots.reduce((acc, slot) => {
      const date = slot.date
      if (!acc[date]) {
        acc[date] = []
      }
      acc[date].push(slot)
      return acc
    }, {} as Record<string, typeof slots>)

    const dates = Object.keys(slotsByDate).sort()
    const slotsInfo = Object.entries(slotsByDate).map(([date, dateSlots]) => {
      const formattedDate = format(new Date(date), 'dd/MM/yyyy', { locale: vi })
      const fieldTimes = dateSlots.map(slot => `${slot.fieldName ?? getFieldName(slot.field)} (${slot.time})`).join(', ')
      return { date: formattedDate, fieldTimes }
    })

    return { dates, slotsInfo }
  }

  // Filter bookings based on tab, search, and filters
  const filteredBookings = useMemo(() => {
    return bookings.filter((booking: AdminBookingItem) => {
      // Filter by tab
      if (bookingTab === 'pending' && booking.status !== 'pending') {
        return false
      }
      if (bookingTab === 'confirmed' && booking.status !== 'confirmed') {
        return false
      }
      if (bookingTab === 'cancelled' && booking.status !== 'cancelled') {
        return false
      }

      // Filter by search query
      if (searchQuery && !booking.customerName.toLowerCase().includes(searchQuery.toLowerCase())
        && !booking.customerPhone.includes(searchQuery)
        && !booking.customerEmail.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false
      }

      // Filter by status
      if (statusFilter !== 'all' && booking.status !== statusFilter) {
        return false
      }

      // Filter by date (simplified for demo)
      const today = new Date().toISOString().split('T')[0]
      const tomorrow = new Date(Date.now() + 86400000).toISOString().split('T')[0]

      if (dateFilter === 'today' && booking.bookingDate !== today) {
        return false
      }
      if (dateFilter === 'tomorrow' && booking.bookingDate !== tomorrow) {
        return false
      }

      // Simplified week filter - in a real app, this would be more sophisticated
      if (dateFilter === 'thisWeek') {
        const bookingDate = new Date(booking.bookingDate)
        const now = new Date()
        const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()))
        const endOfWeek = new Date(now.setDate(now.getDate() + 6))

        if (bookingDate < startOfWeek || bookingDate > endOfWeek) {
          return false
        }
      }

      return true
    })
  }, [bookings, bookingTab, searchQuery, statusFilter, dateFilter])

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">Quản lý đặt lịch</h2>
        <div className="flex gap-2">
          <Button
            variant="outline"
            className="flex items-center gap-1"
            onClick={refetch}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Làm mới
          </Button>
          <Button variant="outline" className="flex items-center gap-1">
            <Download className="h-4 w-4" />
            Xuất Excel
          </Button>
          <Button className="flex items-center gap-1">
            <Plus className="h-4 w-4" />
            Tạo đặt lịch
          </Button>
        </div>
      </div>

      <Tabs value={bookingTab} onValueChange={setBookingTab}>
        <TabsList className="grid grid-cols-4 w-full md:w-[400px]">
          <TabsTrigger value="all">Tất cả</TabsTrigger>
          <TabsTrigger value="pending">Chờ xác nhận</TabsTrigger>
          <TabsTrigger value="confirmed">Đã xác nhận</TabsTrigger>
          <TabsTrigger value="cancelled">Đã hủy</TabsTrigger>
        </TabsList>
      </Tabs>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
            <div className="relative flex-1 w-full md:max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Tìm kiếm theo tên, email, số điện thoại..."
                className="pl-10"
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2 w-full md:w-auto">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder="Trạng thái" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả trạng thái</SelectItem>
                  <SelectItem value="pending">Chờ xác nhận</SelectItem>
                  <SelectItem value="confirmed">Đã xác nhận</SelectItem>
                  <SelectItem value="cancelled">Đã hủy</SelectItem>
                </SelectContent>
              </Select>
              <Select value={dateFilter} onValueChange={setDateFilter}>
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder="Thời gian" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả thời gian</SelectItem>
                  <SelectItem value="today">Hôm nay</SelectItem>
                  <SelectItem value="tomorrow">Ngày mai</SelectItem>
                  <SelectItem value="thisWeek">Tuần này</SelectItem>
                  <SelectItem value="thisMonth">Tháng này</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="text-red-500 text-sm mb-4 p-3 bg-red-50 rounded-md">
              {error}
            </div>
          )}

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Khách hàng</TableHead>
                  <TableHead>Thời gian đặt</TableHead>
                  <TableHead>Trạng thái</TableHead>
                  <TableHead>Phương thức thanh toán</TableHead>
                  <TableHead className="text-right">Thao tác</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading
                  ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-6 text-gray-500">
                          Đang tải dữ liệu...
                        </TableCell>
                      </TableRow>
                    )
                  : filteredBookings.length === 0
                    ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-6 text-gray-500">
                            Không có đặt lịch nào phù hợp với bộ lọc
                          </TableCell>
                        </TableRow>
                      )
                    : (
                        filteredBookings.map((booking: AdminBookingItem) => {
                          const slotsData = formatBookingSlots(booking.bookingSlots)
                          return (
                            <TableRow key={booking._id}>
                              <TableCell>
                                <div>
                                  <div className="font-medium">{booking.customerName}</div>
                                  <div className="text-sm text-gray-500">{booking.customerPhone}</div>
                                  <div className="text-sm text-gray-500">{booking.customerEmail}</div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-start">
                                  <Calendar className="h-4 w-4 mr-2 text-gray-500 mt-1" />
                                  <div className="text-sm">
                                    <div className="font-medium mb-2">
                                      {format(new Date(booking.bookingDate), 'dd/MM/yyyy', { locale: vi })}
                                    </div>
                                    <div className="space-y-2">
                                      {slotsData.slotsInfo !== 'Không có slot'
                                        ? (
                                            (slotsData.slotsInfo as Array<{ date: string, fieldTimes: string }>).map(slotInfo => (
                                              <div key={slotInfo.date} className="bg-gray-50 p-2 rounded">
                                                <div className="text-sm font-medium text-gray-700">{slotInfo.fieldTimes}</div>
                                              </div>
                                            ))
                                          )
                                        : (
                                            <div className="text-gray-500">Không có slot</div>
                                          )}
                                    </div>
                                    <div className="text-xs text-gray-400 mt-2 pt-2 border-t border-gray-100">
                                      Tạo:
                                      {' '}
                                      {format(new Date(booking.createdAt), 'dd/MM/yyyy HH:mm', { locale: vi })}
                                    </div>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge
                                  variant={
                                    booking.status === 'confirmed'
                                      ? 'default'
                                      : booking.status === 'pending' ? 'outline' : 'destructive'
                                  }
                                >
                                  {booking.status === 'confirmed'
                                    ? 'Đã xác nhận'
                                    : booking.status === 'pending' ? 'Chờ xác nhận' : 'Đã hủy'}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <div className="text-sm">
                                  {booking.paymentMethod === 'COD' ? 'Thanh toán khi nhận' : booking.paymentMethod}
                                </div>
                              </TableCell>
                              <TableCell className="text-right">
                                <Button variant="ghost" size="sm">Chi tiết</Button>
                              </TableCell>
                            </TableRow>
                          )
                        })
                      )}
              </TableBody>
            </Table>
          </div>

          {total > 0 && (
            <div className="flex items-center justify-between mt-4 text-sm text-gray-500">
              <div>
                Hiển thị
                {' '}
                {filteredBookings.length}
                {' '}
                /
                {' '}
                {total}
                {' '}
                đặt lịch
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default BookingPageBookings
