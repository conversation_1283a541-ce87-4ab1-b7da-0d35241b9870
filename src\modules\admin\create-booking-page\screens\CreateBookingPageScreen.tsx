'use client'

import type { ValidationResult } from '../hooks/useCreateBookingPage'
import type { SetupState } from '../types/types'
import React, { useCallback, useReducer, useRef } from 'react'
import HeaderSection from '../components/ConfigPage/HeaderSection'
import MainContent from '../components/ConfigPage/MainContent'
import SetupModal from '../components/ConfigPage/SetupModal'
import { useCreateBookingPage } from '../hooks/useCreateBookingPage'
import { useTemplateSelection } from '../hooks/useTemplateSelection'
import { quickConfigHelper } from '../utils/quick-config-helper'
import { globalStyles } from './styles'

/**
 * CreateBookingPageScreen Component
 *
 * Main screen for creating a new booking page.
 * This component has been refactored to:
 * 1. Use custom hooks for logic separation
 * 2. Use smaller, focused components for better organization
 * 3. Improve maintainability and readability
 * 4. Use a modal for setup configuration
 */
const CreateBookingPageScreen = () => {
  // Custom hooks for template selection and page creation
  const { selectedTemplate, handleTemplateSelect } = useTemplateSelection()
  const { isSubmiting, handleSubmitPage, validateBookingPage } = useCreateBookingPage()
  const contentRef = useRef<HTMLDivElement>(null)

  // Consolidated state for setup using useReducer instead of useState
  // This avoids ESLint warnings about setState in useEffect
  const setupReducer = (state: SetupState, updates: Partial<SetupState>): SetupState => {
    return { ...state, ...updates }
  }

  const [setupState, dispatch] = useReducer(setupReducer, {
    isModalOpen: true, // Open by default
    activeTab: 'info',
    isConfigComplete: false,
  })

  // Alias for dispatch to make the code more readable
  const updateSetupState = useCallback((updates: Partial<SetupState>) => {
    dispatch(updates)
  }, [])

  // Check configuration status when component mounts
  // useEffect(() => {
  //   const validationResult = validateBookingPage()
  //   updateSetupState({ isConfigComplete: validationResult.isValid })
  // }, [validateBookingPage, updateSetupState])

  // Handle validation failure
  const handleValidationFailure = useCallback((result: ValidationResult) => {
    // Open the setup modal if it's not already open
    updateSetupState({ isModalOpen: true })

    // Switch to the appropriate tab based on error type
    if (result.errorType === 'pageInfo') {
      // Dispatch custom event to highlight missing fields in the form
      window.dispatchEvent(new CustomEvent('highlight-missing-fields'))
      updateSetupState({ activeTab: 'info' })
    }

    // Scroll to the content area if needed
    if (contentRef.current) {
      contentRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [updateSetupState])

  // Handle create page with validation
  const handleSubmitPageWithValidation = () => {
    // First validate directly
    const validationResult = validateBookingPage()

    // If validation fails, handle it immediately
    if (!validationResult.isValid) {
      handleValidationFailure(validationResult)
      return
    }

    // If validation passes, proceed with creating the page
    handleSubmitPage()
  }

  // Check if configuration is complete
  const checkConfigComplete = () => {
    const validationResult = validateBookingPage()
    updateSetupState({ isConfigComplete: validationResult.isValid })

    if (!validationResult.isValid) {
      handleValidationFailure(validationResult)
      return false
    }

    return true
  }

  return (
    <div className="w-full h-screen flex flex-col overflow-hidden">
      {/* Header with setup button */}
      <HeaderSection
        onOpenSetup={() => updateSetupState({ isModalOpen: true })}
        onSubmitPage={handleSubmitPageWithValidation}
        isSubmiting={isSubmiting}
        isConfigComplete={setupState.isConfigComplete}
      />

      {/* Main content area */}
      <MainContent
        isConfigComplete={setupState.isConfigComplete}
        onOpenSetup={() => updateSetupState({ isModalOpen: true })}
        onOpenConfigBookingPage={() => quickConfigHelper()?.open()}
        contentRef={contentRef}
      />

      {/* Setup Modal */}
      <SetupModal
        state={setupState}
        onStateChange={updateSetupState}
        selectedTemplate={selectedTemplate}
        handleTemplateSelect={handleTemplateSelect}
        onValidate={checkConfigComplete}
      />

      {/* Global styles */}
      <style jsx global>
        {globalStyles}
      </style>
    </div>
  )
}

export default CreateBookingPageScreen
