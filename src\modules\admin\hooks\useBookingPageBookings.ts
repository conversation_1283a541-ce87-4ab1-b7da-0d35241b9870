import type { AdminBookingItem } from '@/modules/admin/apis/booking-page.api'
import { bookingPageAPIs } from '@/modules/admin/apis/booking-page.api'
import { useCallback, useEffect, useState } from 'react'
import { toast } from 'sonner'

interface UseBookingPageBookingsParams {
  bookingPageId: string
  status?: string
  startDate?: string
  endDate?: string
  limit?: number
  offset?: number
}

interface UseBookingPageBookingsReturn {
  bookings: AdminBookingItem[]
  isLoading: boolean
  error: string | null
  total: number
  limit: number
  offset: number
  fetchBookings: () => Promise<void>
  refetch: () => Promise<void>
}

/**
 * Custom hook to fetch and manage booking page bookings
 */
export const useBookingPageBookings = (params: UseBookingPageBookingsParams): UseBookingPageBookingsReturn => {
  const [bookings, setBookings] = useState<AdminBookingItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [total, setTotal] = useState(0)
  const [limit, setLimit] = useState(20)
  const [offset, setOffset] = useState(0)

  // Fetch bookings from API
  const fetchBookings = useCallback(async () => {
    if (!params.bookingPageId) {
      setBookings([])
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      const apiParams = {
        status: params.status,
        startDate: params.startDate,
        endDate: params.endDate,
        limit: params.limit || 20,
        offset: params.offset || 0,
      }

      // Remove undefined values
      const cleanParams = Object.fromEntries(
        Object.entries(apiParams).filter(([_, value]) => value !== undefined)
      )

      const response = await bookingPageAPIs.getBookingPageBookings(params.bookingPageId, cleanParams)

      if (response && response.status?.success && response.data) {
        const bookingsData = Array.isArray(response.data.data)
          ? response.data.data
          : []
        
        setBookings(bookingsData)
        setTotal(response.data.meta?.total || 0)
        setLimit(response.data.meta?.limit || 20)
        setOffset(response.data.meta?.offset || 0)
      } else {
        setBookings([])
        setTotal(0)
        setError('Không thể tải danh sách đặt lịch')
      }
    } catch (err) {
      console.error('Failed to fetch bookings:', err)
      setError('Không thể tải danh sách đặt lịch. Vui lòng thử lại sau.')
      setBookings([])
      setTotal(0)
      toast.error('Không thể tải danh sách đặt lịch')
    } finally {
      setIsLoading(false)
    }
  }, [params.bookingPageId, params.status, params.startDate, params.endDate, params.limit, params.offset])

  // Refetch function (alias for fetchBookings)
  const refetch = useCallback(() => {
    return fetchBookings()
  }, [fetchBookings])

  // Load bookings on mount and when params change
  useEffect(() => {
    fetchBookings()
  }, [fetchBookings])

  return {
    bookings,
    isLoading,
    error,
    total,
    limit,
    offset,
    fetchBookings,
    refetch,
  }
}

export default useBookingPageBookings
